import 'package:json_annotation/json_annotation.dart';

part 'grade_model.g.dart';

@JsonSerializable()
class GradeModel {
  @Json<PERSON>ey(name: 'Id')
  String? id;
  @Json<PERSON>ey(name: 'Name', defaultValue: '')
  String? name;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'LevelSequenceenum')
  int? levelSequenceenum;
  @Json<PERSON>ey(name: 'OrderIndex')
  dynamic orderIndex;

  GradeModel({this.id, this.name = '', this.levelSequenceenum, this.orderIndex});

  factory GradeModel.fromJson(Map<String, dynamic> json) {
    return _$GradeModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$GradeModelToJson(this);
}
