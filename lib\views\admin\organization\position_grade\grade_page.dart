import 'package:flutter/material.dart';
import 'package:octasync_client/api/positions.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/views/admin/organization/position_grade/grade_dialog.dart';
import 'package:octasync_client/views/admin/organization/position_grade/grade_model/grade_model.dart';

/// 职级页面
class GradePage extends StatefulWidget {
  const GradePage({super.key});

  @override
  State<GradePage> createState() => _GradePageState();
}

class _GradePageState extends State<GradePage> {
  // 创建 GlobalKey 用于访问 DepartmentSelector 的方法
  final GlobalKey<GradeDialogState> _gradeDialogStateKey = GlobalKey<GradeDialogState>();
  List<GradeModel> gradeList = [];

  @override
  void initState() {
    super.initState();
    final resJson = PositionsApi.getListPage({});
    List<GradeModel> gradeList =
        (resJson.Items as List).map((item) => GradeModel.fromJson(item)).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Column(children: []),
        GradeDialog(
          key: _gradeDialogStateKey,
          child: AppButton(
            text: '添加职级',
            type: ButtonType.primary,
            onPressed: () {
              _gradeDialogStateKey.currentState?.showGradeDialog(context);
            },
          ),
        ),
      ],
    );
  }
}
